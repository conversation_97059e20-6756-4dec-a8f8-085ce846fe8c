import { defineComponent, ref, watch, shallowReactive } from 'vue'
import * as echarts from 'echarts'

// 声明类型
const PropsType = {
  cdata: {
    type: Object,
    require: true,
  },
} as const

// 定义主体
export default defineComponent({
  props: PropsType,
  setup(props) {
    // 定义 ref
    const chartRef = ref()
    // 配置项
    let options = {}

    watch(
      () => props.cdata,
      (val: any) => {
        options = {
          title: {
            subtext: '物资仓库A',
            //位置
            top: 32,
            left: 200,

            subtextStyle: {
              fontFamily: 'alia',
              fontSize: 14,
              color:    color: {

                                type: 'linear',

                                x: 1,

                                y: 0,

                                x2: 0,

                                y2: 0,

                                colorStops: [

                                  {

                                    offset: 0, color: '#04D37C' // 0% 处的颜色

                                  },

                                  {

                                    offset: 1, color: '#008C8C' // 100% 处的颜色

                                  }

                                ],

                                global: false // 缺省为 false

                              }
————————————————
版权声明：本文为CSDN博主「susu1083018911」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/susu1083018911/article/details/142524400
              //  color: 'rgba(255, 205, 97, .6)',
            },
          },
          //改为透明
          backgroundColor: 'transparent',
          legend: {
            //设置icon宽高
            itemWidth: 15,
            itemHeight: 4,
            //设置icon位置
            right: 5,
            top: 2,
            //设置icon 温度 湿度间距
            itemGap: 30,
            icon: 'rect',
            textStyle: {
              fontSize: 14,
              color: 'rgba(255,255,255,.8)',
            },
            data: [
              {
                name: '温度',
                itemStyle: {
                  color: '#2ca8ff',
                },
              },
              {
                name: '湿度',
                itemStyle: {
                  color: '#81e279',
                },
              },
            ],
          },
          grid: {
            top: '28%',
            left: '5%',
            right: '5%',
            bottom: '5%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'transparent',
            axisPointer: {
              lineStyle: {
                color: '#3763cd', // 显示竖线颜色
                type: 'solid',
              },
            },
            textStyle: {
              color: '#ffffff',
            },
          },
          xAxis: [
            {
              boundaryGap: false,
              type: 'category',
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#485051',
                  type: 'dashed',
                },
                onZero: false,
              },
              axisLabel: {
                show: true,
                interval: 0,
                textStyle: {
                  color: '#abb2af',

                  padding: 10,
                  fontSize: 14,
                },
              },
              axisTick: {
                show: false,
              },
              data: [
                '01',
                '02',
                '03',
                '04',
                '05',
                '06',
                '07',
                '08',
                '09',
                '10',
                '11',
                '12',
              ],
            },
          ],
          yAxis: [
            {
              name: '℃',
              max: 60,
              min: -20,
              nameTextStyle: {
                color: '#abb2af',
                fontSize: 14,
                padding: [0, 10, 10, -40],
              },

              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  width: 1,
                  color: 'rgba(255, 255, 255, 0.2)',
                },
              },
              axisLine: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#abb2af',

                  padding: 10,
                  fontSize: 14,
                },
              },
              axisTick: {
                show: false,
              },
            },
            {
              name: '%RH',
              max: 80,
              min: 0,
              position: 'right',
              nameTextStyle: {
                color: '#abb2af',

                fontSize: 14,
                padding: [0, -25, 10, 0],
              },

              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  width: 1,
                  color: 'rgba(255, 255, 255, 0.2)',
                },
              },
              axisLine: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#abb2af',

                  fontSize: 14,
                },
              },
              axisTick: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: '温度',
              type: 'line',
              smooth: false,
              showSymbol: false,
              lineStyle: {
                normal: {
                  width: 2,
                  color: '#2C9DEC', // 线条颜色
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(44,157,236,0.5)',
                      },
                      {
                        offset: 1,
                        color: 'rgba(44,157,236,0)',
                      },
                    ],
                    false,
                  ),
                },
              },
              data: [22, 41, 45, 48, 55, 33, 44, 20, 34, 40, 47, 20, 100],
            },
            {
              name: '湿度',
              type: 'line',
              yAxisIndex: 1,
              smooth: false,
              showSymbol: false,
              lineStyle: {
                normal: {
                  width: 2,
                  color: '#81e279', // 线条颜色
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(131,225,175,0.5)',
                      },
                      {
                        offset: 1,
                        color: 'rgba(131,225,175,0)',
                      },
                    ],
                    false,
                  ),
                },
              },
              data: [58, 18, 30, 25, 40, 62, 38, 37, 20, 30, 49, 60],
            },
          ],
        }

        // 手动触发更新
        if (chartRef.value) {
          // 通过初始化参数打入数据
          chartRef.value.initChart(options)
        }
      },
      {
        immediate: true,
        deep: true,
      },
    )

    return () => {
      const height = '300px'
      const width = '480px'

      return (
        <div>
          <echart ref={chartRef} options={options} height={height} width={width} />
        </div>
      )
    }
  },
})
