<template>
  <div class="centerRight2">
    <dv-scroll-board class="dv-scr-board" :config="config" />
  </div>
</template>

<script setup>
import { defineComponent, reactive } from 'vue'
    const config = reactive({
      header: ['序号','仓库', '物资名称', '库存/预警值'],
      data: [
        ['<span style="color:#37a2da;">行1列</span>', '<span style="width:300px">行1列asd无的放矢电风扇地方水电费防守打法手动蝶阀是撒代发</span>', '行1列3'],
        ['行2列1', '<span style="color:#32c5e9;">行2列2</span>', '行2列3'],
        ['行3列1', '行3列2', '<span style="color:#67e0e3;">行3列3</span>'],
        ['行4列1', '<span style="color:#9fe6b8;">行4列2</span>', '行4列3'],
        ['<span style="color:#ffdb5c;">行5列1</span>', '行5列2', '行5列3'],
        ['行6列1', '<span style="color:#ff9f7f;">行6列2</span>', '行6列3'],
        ['行7列1', '行7列2', '<span style="color:#fb7293;">行7列3</span>'],
        ['行8列1', '<span style="color:#e062ae;">行8列2</span>', '行8列3'],
        ['<span style="color:#e690d1;">行9列1</span>', '行9列2', '行9列3'],
        ['行10列1', '<span style="color:#e7bcf3;">行10列2</span>', '行10列3'],
      ],

      rowNum: 6, //表格行数
      headerHeight: 36,
      headerBGC: '#103F3B', //表头
      oddRowBGC: '#08332C', //奇数行
      evenRowBGC: '#0F3E34', //偶数行
        align: ['center'],
        // waitTime: 100000,
    })
</script>

<style lang="scss" scoped>
$box-height: 257px;
$box-width: 812px;
.centerRight2 {
  height: $box-height;
  width: $box-width;

  .text {
    color: #c3cbde;
  }
}
 :deep(.dv-scroll-board .rows .row-item) {
    display: flex;
    font-size: 14px;
    transition: all 0.3s;
    margin-top: 6px !important;
}
</style>
